{"name": "parv_event_dashboard", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=18"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.1", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.3", "@mui/system": "^5.14.3", "@mui/x-charts": "^6.0.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.6", "@reduxjs/toolkit": "^1.9.5", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^4.32.6", "@tanstack/react-table": "^8.9.3", "@tiptap/extension-character-count": "^2.1.6", "@tiptap/extension-code-block-lowlight": "^2.1.6", "@tiptap/extension-color": "^2.1.6", "@tiptap/extension-dropcursor": "^2.1.6", "@tiptap/extension-focus": "^2.1.6", "@tiptap/extension-gapcursor": "^2.1.6", "@tiptap/extension-hard-break": "^2.1.6", "@tiptap/extension-highlight": "^2.1.6", "@tiptap/extension-horizontal-rule": "^2.1.6", "@tiptap/extension-image": "^2.1.6", "@tiptap/extension-link": "^2.1.6", "@tiptap/extension-list-item": "^2.1.6", "@tiptap/extension-mention": "^2.1.6", "@tiptap/extension-placeholder": "^2.1.6", "@tiptap/extension-subscript": "^2.1.6", "@tiptap/extension-superscript": "^2.1.6", "@tiptap/extension-table": "^2.1.6", "@tiptap/extension-table-cell": "^2.1.6", "@tiptap/extension-table-header": "^2.1.6", "@tiptap/extension-table-row": "^2.1.6", "@tiptap/extension-task-item": "^2.1.6", "@tiptap/extension-task-list": "^2.1.6", "@tiptap/extension-text-align": "^2.1.6", "@tiptap/extension-text-style": "^2.1.6", "@tiptap/extension-typography": "^2.1.6", "@tiptap/extension-underline": "^2.1.6", "@tiptap/pm": "^2.1.6", "@tiptap/react": "^2.1.6", "@tiptap/starter-kit": "^2.1.6", "@types/chart.js": "^2.9.37", "axios": "^1.4.0", "chart.js": "^4.3.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "highlight.js": "^11.8.0", "lowlight": "^2.9.0", "lucide-react": "^0.144.0", "quill-image-uploader": "^1.3.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-icons": "^4.10.1", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-redux": "^8.1.2", "react-router-dom": "^6.14.2", "react-toastify": "^9.1.3", "recharts": "^2.7.2", "shadcn-ui": "^0.3.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6", "zod": "^3.21.4", "zustand": "^4.4.1"}, "devDependencies": {"@eslint/js": "^8.44.0", "@types/node": "^18.16.19", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-redux": "^7.1.25", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "globals": "^13.20.0", "postcss": "^8.4.27", "rollup": "^3.26.3", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5"}, "overrides": {"rollup": "^3.26.3"}, "resolutions": {"lodash.template": "^4.5.0", "@humanwhocodes/object-schema": "^2.0.3", "@humanwhocodes/config-array": "^0.13.0", "eslint": "^8.57.1", "@mui/base": "^5.0.0-beta.30"}}